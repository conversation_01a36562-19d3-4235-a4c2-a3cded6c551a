"""
Скрипт для создания таблиц домашних заданий в базе данных
"""
import asyncio
import sys
import os

# Добавляем корневую папку проекта в путь
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from database import init_database
from database.models import Base
from database.database import engine


async def create_homework_tables():
    """Создание таблиц для домашних заданий"""
    print("🔄 Создание таблиц для домашних заданий...")
    
    try:
        # Создаем все таблицы (включая новые)
        async with engine.begin() as conn:
            await conn.run_sync(Base.metadata.create_all)
        
        print("✅ Таблицы домашних заданий успешно созданы!")
        print("📋 Созданы таблицы:")
        print("   - homeworks (домашние задания)")
        print("   - questions (вопросы)")
        print("   - answer_options (варианты ответов)")
        
    except Exception as e:
        print(f"❌ Ошибка при создании таблиц: {e}")
        return False
    
    return True


async def main():
    """Основная функция"""
    print("🚀 Запуск скрипта обновления базы данных для домашних заданий")
    
    # Инициализируем подключение к базе данных
    await init_database()
    
    # Создаем таблицы
    success = await create_homework_tables()
    
    if success:
        print("\n🎉 Обновление базы данных завершено успешно!")
        print("💡 Теперь можно использовать функционал домашних заданий")
    else:
        print("\n❌ Обновление базы данных завершилось с ошибками")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
