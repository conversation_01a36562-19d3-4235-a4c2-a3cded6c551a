"""
Тест для проверки сохранения photo_path в вопросах
"""
import asyncio
import sys
import os

# Добавляем корневую папку проекта в путь
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from database import (
    init_database, HomeworkRepository, QuestionRepository, 
    AnswerOptionRepository, UserRepository, CourseRepository,
    SubjectRepository, LessonRepository
)


async def test_photo_path():
    """Тест сохранения photo_path"""
    print("🧪 Тестирование сохранения photo_path в вопросах...")
    
    try:
        # Инициализируем БД
        await init_database()
        
        # Получаем существующие данные
        users = await UserRepository.get_all()
        courses = await CourseRepository.get_all()
        subjects = await SubjectRepository.get_all()
        lessons = await LessonRepository.get_all()
        
        if not users or not courses or not subjects or not lessons:
            print("❌ Нет базовых данных для теста")
            return
        
        # Находим менеджера
        manager = None
        for user in users:
            if user.role == 'manager':
                manager = user
                break
        
        if not manager:
            print("❌ Не найден менеджер")
            return
        
        # Создаем тестовое ДЗ
        homework = await HomeworkRepository.create(
            name="Тест photo_path",
            course_id=courses[0].id,
            subject_id=subjects[0].id,
            lesson_id=lessons[0].id,
            created_by=manager.id
        )
        
        print(f"✅ Создано ДЗ: {homework.name} (ID: {homework.id})")
        
        # Создаем вопрос с фото
        test_file_id = "BAADBAADrwADBREAAWdXAAFYVoydlwI"  # Тестовый file_id
        
        question = await QuestionRepository.create(
            homework_id=homework.id,
            text="Тестовый вопрос с фото",
            photo_path=test_file_id,
            time_limit=60
        )
        
        print(f"✅ Создан вопрос: {question.text} (ID: {question.id})")
        print(f"📷 Photo path: {question.photo_path}")
        
        # Создаем варианты ответов
        await AnswerOptionRepository.create_multiple(question.id, [
            {"text": "Вариант A", "is_correct": True},
            {"text": "Вариант B", "is_correct": False},
            {"text": "Вариант C", "is_correct": False}
        ])
        
        print("✅ Созданы варианты ответов")
        
        # Проверяем, что photo_path сохранился
        saved_question = await QuestionRepository.get_by_id(question.id)
        if saved_question and saved_question.photo_path == test_file_id:
            print(f"✅ Photo path корректно сохранен: {saved_question.photo_path}")
        else:
            print(f"❌ Photo path не сохранился или неверный: {saved_question.photo_path if saved_question else 'None'}")
        
        # Получаем полную информацию о ДЗ
        full_homework = await HomeworkRepository.get_by_id(homework.id)
        if full_homework and full_homework.questions:
            question_with_photo = full_homework.questions[0]
            print(f"✅ Вопрос из ДЗ: {question_with_photo.text}")
            print(f"📷 Photo path из ДЗ: {question_with_photo.photo_path}")
        
        # Удаляем тестовое ДЗ
        await HomeworkRepository.delete(homework.id)
        print("🗑 Тестовое ДЗ удалено")
        
        print("🎉 Тест завершен успешно!")
        
    except Exception as e:
        print(f"❌ Ошибка в тесте: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(test_photo_path())
