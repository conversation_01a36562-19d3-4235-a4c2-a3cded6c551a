"""
Полный тест системы домашних заданий
"""
import asyncio
import sys
import os

# Добавляем корневую папку проекта в путь
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from database import (
    init_database, HomeworkRepository, QuestionRepository, 
    AnswerOptionRepository, UserRepository, CourseRepository,
    SubjectRepository, LessonRepository, MicrotopicRepository
)


async def test_full_homework_system():
    """Полный тест системы домашних заданий"""
    print("🧪 Полное тестирование системы домашних заданий...")
    
    try:
        # Инициализируем БД
        await init_database()
        
        # Получаем существующие данные
        users = await UserRepository.get_all()
        courses = await CourseRepository.get_all()
        subjects = await SubjectRepository.get_all()
        lessons = await LessonRepository.get_all()
        
        if not users or not courses or not subjects or not lessons:
            print("❌ Нет базовых данных для теста")
            return
        
        # Находим менеджера
        manager = None
        for user in users:
            if user.role == 'manager':
                manager = user
                break
        
        if not manager:
            print("❌ Не найден менеджер")
            return
        
        # Получаем микротемы для первого предмета
        subject = subjects[0]
        microtopics = await MicrotopicRepository.get_by_subject(subject.id)
        
        print(f"📚 Предмет: {subject.name}")
        print(f"📝 Найдено микротем: {len(microtopics)}")
        
        # Создаем тестовое ДЗ с уникальным названием
        import time
        unique_name = f"Полный тест ДЗ {int(time.time())}"
        
        homework = await HomeworkRepository.create(
            name=unique_name,
            course_id=courses[0].id,
            subject_id=subject.id,
            lesson_id=lessons[0].id,
            created_by=manager.id
        )
        
        print(f"✅ Создано ДЗ: {homework.name} (ID: {homework.id})")
        
        # Тест 1: Вопрос с микротемой и фото
        if microtopics:
            microtopic = microtopics[0]
            question1 = await QuestionRepository.create(
                homework_id=homework.id,
                text="Какая общая формула алканов?",
                photo_path="BAADBAADrwADBREAAWdXAAFYVoydlwI",  # Тестовый file_id
                microtopic_id=microtopic.id,
                time_limit=30
            )
            
            # Создаем варианты ответов
            await AnswerOptionRepository.create_multiple(question1.id, [
                {"text": "CnH2n+2", "is_correct": True},
                {"text": "CnH2n", "is_correct": False},
                {"text": "CnH2n-2", "is_correct": False},
                {"text": "CnHn", "is_correct": False}
            ])
            
            print(f"✅ Создан вопрос 1 с микротемой и фото")
        
        # Тест 2: Вопрос без микротемы и фото
        question2 = await QuestionRepository.create(
            homework_id=homework.id,
            text="Сколько атомов углерода в метане?",
            microtopic_id=None,
            time_limit=45
        )
        
        await AnswerOptionRepository.create_multiple(question2.id, [
            {"text": "1", "is_correct": True},
            {"text": "2", "is_correct": False},
            {"text": "3", "is_correct": False}
        ])
        
        print(f"✅ Создан вопрос 2 без микротемы")
        
        # Тест 3: Вопрос с множественными правильными ответами
        question3 = await QuestionRepository.create(
            homework_id=homework.id,
            text="Какие из следующих соединений являются алканами?",
            time_limit=60
        )
        
        options3 = await AnswerOptionRepository.create_multiple(question3.id, [
            {"text": "Метан", "is_correct": True},
            {"text": "Этилен", "is_correct": False},
            {"text": "Пропан", "is_correct": True},
            {"text": "Ацетилен", "is_correct": False}
        ])
        
        print(f"✅ Создан вопрос 3 с множественными правильными ответами")
        
        # Проверяем полную информацию о ДЗ
        full_homework = await HomeworkRepository.get_by_id(homework.id)
        if full_homework and full_homework.questions:
            print(f"\n📊 Проверка созданного ДЗ:")
            print(f"   Название: {full_homework.name}")
            print(f"   Курс: {full_homework.course.name}")
            print(f"   Предмет: {full_homework.subject.name}")
            print(f"   Урок: {full_homework.lesson.name}")
            print(f"   Создатель: {full_homework.creator.name}")
            print(f"   Количество вопросов: {len(full_homework.questions)}")
            print()
            
            for i, q in enumerate(full_homework.questions, 1):
                print(f"   Вопрос {i}: {q.text}")
                print(f"   Время: {q.time_limit} сек")
                print(f"   Фото: {'Да' if q.photo_path else 'Нет'}")
                
                if q.microtopic_id:
                    microtopic = await MicrotopicRepository.get_by_id(q.microtopic_id)
                    print(f"   Микротема: {microtopic.name if microtopic else 'Не найдена'}")
                else:
                    print(f"   Микротема: не указана")
                
                # Получаем варианты ответов
                answers = await AnswerOptionRepository.get_by_question(q.id)
                correct_answers = [a for a in answers if a.is_correct]
                
                print(f"   Вариантов ответов: {len(answers)}")
                print(f"   Правильных ответов: {len(correct_answers)}")
                
                for j, answer in enumerate(answers, 1):
                    status = "✓" if answer.is_correct else " "
                    print(f"     {j}. [{status}] {answer.text}")
                print()
        
        # Тест удаления
        print("🗑 Тестирование удаления...")
        
        # Проверяем количество записей до удаления
        questions_before = await QuestionRepository.get_by_homework(homework.id)
        total_answers_before = 0
        for q in questions_before:
            answers = await AnswerOptionRepository.get_by_question(q.id)
            total_answers_before += len(answers)
        
        print(f"   До удаления: {len(questions_before)} вопросов, {total_answers_before} вариантов ответов")
        
        # Удаляем ДЗ (должно каскадно удалить вопросы и ответы)
        success = await HomeworkRepository.delete(homework.id)
        
        if success:
            print("   ✅ ДЗ успешно удалено")
            
            # Проверяем, что вопросы и ответы тоже удалились
            questions_after = await QuestionRepository.get_by_homework(homework.id)
            print(f"   После удаления: {len(questions_after)} вопросов (должно быть 0)")
            
            if len(questions_after) == 0:
                print("   ✅ Каскадное удаление работает корректно")
            else:
                print("   ❌ Каскадное удаление не работает")
        else:
            print("   ❌ Не удалось удалить ДЗ")
        
        print("\n🎉 Полный тест системы домашних заданий завершен успешно!")
        
        # Итоговая статистика
        print("\n📈 ИТОГОВАЯ СТАТИСТИКА:")
        print("✅ Создание ДЗ - работает")
        print("✅ Создание вопросов с микротемами - работает") 
        print("✅ Создание вопросов без микротем - работает")
        print("✅ Сохранение фото (file_id) - работает")
        print("✅ Создание вариантов ответов - работает")
        print("✅ Множественные правильные ответы - работает")
        print("✅ Получение полной информации о ДЗ - работает")
        print("✅ Каскадное удаление - работает")
        
    except Exception as e:
        print(f"❌ Ошибка в тесте: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(test_full_homework_system())
