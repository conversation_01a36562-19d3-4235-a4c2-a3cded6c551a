"""
Тест для проверки сохранения microtopic_id в вопросах
"""
import asyncio
import sys
import os

# Добавляем корневую папку проекта в путь
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from database import (
    init_database, HomeworkRepository, QuestionRepository, 
    AnswerOptionRepository, UserRepository, CourseRepository,
    SubjectRepository, LessonRepository, MicrotopicRepository
)


async def test_microtopic_id():
    """Тест сохранения microtopic_id"""
    print("🧪 Тестирование сохранения microtopic_id в вопросах...")
    
    try:
        # Инициализируем БД
        await init_database()
        
        # Получаем существующие данные
        users = await UserRepository.get_all()
        courses = await CourseRepository.get_all()
        subjects = await SubjectRepository.get_all()
        lessons = await LessonRepository.get_all()
        
        if not users or not courses or not subjects or not lessons:
            print("❌ Нет базовых данных для теста")
            return
        
        # Находим менеджера
        manager = None
        for user in users:
            if user.role == 'manager':
                manager = user
                break
        
        if not manager:
            print("❌ Не найден менеджер")
            return
        
        # Получаем микротемы для первого предмета
        subject = subjects[0]
        microtopics = await MicrotopicRepository.get_by_subject(subject.id)
        
        print(f"📚 Предмет: {subject.name}")
        print(f"📝 Найдено микротем: {len(microtopics)}")
        
        if microtopics:
            for mt in microtopics:
                print(f"   - ID: {mt.id}, Название: {mt.name}")
        
        # Создаем тестовое ДЗ с уникальным названием
        import time
        unique_name = f"Тест microtopic_id {int(time.time())}"

        homework = await HomeworkRepository.create(
            name=unique_name,
            course_id=courses[0].id,
            subject_id=subject.id,
            lesson_id=lessons[0].id,
            created_by=manager.id
        )
        
        print(f"✅ Создано ДЗ: {homework.name} (ID: {homework.id})")
        
        # Тест 1: Вопрос с существующей микротемой
        if microtopics:
            microtopic = microtopics[0]
            question1 = await QuestionRepository.create(
                homework_id=homework.id,
                text="Тестовый вопрос с микротемой",
                microtopic_id=microtopic.id,
                time_limit=60
            )
            
            print(f"✅ Создан вопрос с микротемой: {question1.text}")
            print(f"🔗 Microtopic ID: {question1.microtopic_id}")
            print(f"📋 Название микротемы: {microtopic.name}")
        
        # Тест 2: Вопрос без микротемы
        question2 = await QuestionRepository.create(
            homework_id=homework.id,
            text="Тестовый вопрос без микротемы",
            microtopic_id=None,
            time_limit=30
        )
        
        print(f"✅ Создан вопрос без микротемы: {question2.text}")
        print(f"🔗 Microtopic ID: {question2.microtopic_id}")
        
        # Создаем варианты ответов для обоих вопросов
        for question in [question1 if microtopics else None, question2]:
            if question:
                await AnswerOptionRepository.create_multiple(question.id, [
                    {"text": "Вариант A", "is_correct": True},
                    {"text": "Вариант B", "is_correct": False}
                ])
        
        # Проверяем полную информацию о ДЗ
        full_homework = await HomeworkRepository.get_by_id(homework.id)
        if full_homework and full_homework.questions:
            print(f"\n📊 Проверка сохраненных данных:")
            for i, q in enumerate(full_homework.questions, 1):
                print(f"   Вопрос {i}: {q.text}")
                print(f"   Microtopic ID: {q.microtopic_id}")

                # Получаем микротему отдельно, если ID есть
                if q.microtopic_id:
                    microtopic = await MicrotopicRepository.get_by_id(q.microtopic_id)
                    if microtopic:
                        print(f"   Название микротемы: {microtopic.name}")
                    else:
                        print(f"   Микротема: не найдена по ID {q.microtopic_id}")
                else:
                    print(f"   Микротема: не указана")
                print()
        
        # Удаляем тестовое ДЗ
        await HomeworkRepository.delete(homework.id)
        print("🗑 Тестовое ДЗ удалено")
        
        print("🎉 Тест завершен успешно!")
        
    except Exception as e:
        print(f"❌ Ошибка в тесте: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(test_microtopic_id())
